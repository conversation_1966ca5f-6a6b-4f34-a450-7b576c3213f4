name: RSpec

on: push

# rspec will run if there are Ready for review PR label

jobs:
  rspec:
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: password
        ports:
          - 5432:5432
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5
      redis:
        image: redis:6
        ports:
          - 6379:6379
        options: --health-cmd "redis-cli ping" --health-interval 10s --health-timeout 5s --health-retries 5
    steps:
      - uses: actions/checkout@v3
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: 3.1.2
          bundler-cache: true
      - name: Install PostgreSQL client
        run: |
          sudo apt-get -yqq install libpq-dev
      - name: Setup credentials
        run: |
          echo "${{ secrets.RAILS_MASTER_KEY }}" > config/master.key
      - name: Run tests
        env: 
          RAILS_ENV: test
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: password
          ALLOWED_CORS_ORIGIN: http://localhost:3000
        run : |
          bundle exec rake db:drop db:create db:schema:load
          bundle exec rspec --profile 10 --format progress --fail-fast --order defined
    
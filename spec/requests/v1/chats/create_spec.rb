# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Chats#create', type: :request do
  let!(:test_user) do
    User.create(
      display_name: 'Test',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:organization) do
    Organization.create(name: 'Test')
  end

  let!(:workspace) do
    Workspace.create(
      organization_id: organization.id,
      name: 'Test'
    )
  end

  let!(:membership_test_user) do
    Membership.create(
      user_id: test_user.id,
      organization_id: organization.id
    )
  end

  let!(:workspace_membership) do
    WorkspacesMembership.create(
      membership_id: membership_test_user.id,
      workspace_id: workspace.id
    )
  end

  let!(:model) do
    Model.create(
      name: 'ChatGPT gpt-4o',
      max_tokens: 100,
      temperature: 1.0,
      model: 'gpt-4o',
      instruction: 'Chat GPT',
      openai_assistant_id: 'asst_abc098'
    )
  end

  def create_chat(params, user)
    post '/v1/chats', params, as_user(user)
  end

  before do
    allow_any_instance_of(OpenaiService).to receive(:create_assistant)
                                        .with(anything)
      .and_return({ 'id' => 'asst_xyz123' })
  end

  let(:params) do
    {
      name: 'new chat',
      model: 'gpt-4o',
      chat_type: 'general'
    }
  end

  context 'with valid params & data' do
    before do
      params[:workspace_id] = workspace.id
    end

    it 'return created & create new chat with ownership' do
      create_chat(params, test_user)
      expect_response(:created)

      expect(response_data['id'].present?).to be_truthy
      expect(response_data['id'].class).to eq(Integer)

      new_chat = Chat.find(response_data['id'])

      expect(new_chat.workspace_membership_membership_id).to eq membership_test_user.id
      expect(new_chat.workspace_membership_workspace_id).to eq workspace.id
    end

    it 'return created & create new model if model does not exist yet' do
      params[:model] = 'gpt-4o-mini'
      create_chat(params, test_user)
      expect_response(:created)

      expect(response_data['id'].present?).to be_truthy
      expect(response_data['id'].class).to eq(Integer)

      expect(Model.all.count).to eq 2
      new_model = Model.find_by(model: 'gpt-4o-mini')
      expect(new_model.openai_assistant_id).to eq 'asst_xyz123'
    end
  end

  context 'with workspace & workspace membership not created' do
    it 'return created & create new workspace & workspace membership' do
      params[:workspace_id] = -1

      create_chat(params, test_user)
      expect_response(:created)

      workspace_id = response_data[:workspace_id]
      new_workspace = Workspace.find(workspace_id)
      new_workspace_membership = WorkspacesMembership.find_by(workspace_id: workspace_id)

      expect(new_workspace.organization_id).to eq organization.id
      expect(new_workspace_membership.workspace_id).to eq new_workspace.id
      expect(new_workspace_membership.membership_id).to eq membership_test_user.id
    end
  end

  context 'with workspace & workspace membership created' do
    it 'return created & force workspace to existing workspace' do
      params[:workspace_id] = -192_312

      expect(Workspace.all.count).to eq 1
      expect(WorkspacesMembership.all.count).to eq 1

      create_chat(params, test_user)
      expect_response(:created)

      expect(response_data['id'].present?).to be_truthy
      expect(response_data['id'].class).to eq(Integer)
      expect(response_data['workspace_id']).to eq workspace.id

      expect(Workspace.all.count).to eq 1
      expect(WorkspacesMembership.all.count).to eq 1
    end
  end

  context 'with source model template' do
    let!(:model_template) do
      ModelTemplate.create(
        name: 'Test Template',
        description: 'Test Desc',
        max_tokens: 100,
        temperature: 1.0,
        model: 'gpt-4o',
        instruction: 'Chat GPT',
        prompt: 'test',
        placeholder: 'reference',
        organization_id: organization.id
      )
    end

    let!(:model_from_template) do
      Model.create(
        name: 'Test Template',
        max_tokens: 100,
        temperature: 1.0,
        model: 'gpt-4o',
        instruction: 'Chat GPT',
        openai_assistant_id: 'asst_mba231',
        model_template_id: model_template.id
      )
    end

    it 'return created & create new chat with model from template' do
      params[:workspace_id] = workspace.id
      params[:source_model_template_id] = model_template.id

      create_chat(params, test_user)
      expect_response(:created)

      expect(response_data['id'].present?).to be_truthy
      expect(response_data['id'].class).to eq(Integer)
      expect(response_data['source_model_template']['id']).to eq model_template.id
    end
  end
end

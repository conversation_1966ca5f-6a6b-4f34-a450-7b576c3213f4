require 'rails_helper'

RSpec.describe ::V1::ModelTemplatesController, type: :request do
  let(:test_user) do
    User.create!(
      display_name: 'Test',
      email: '<EMAIL>',
      password: '12345678'
    )
  end
  let(:organization) do
    Organization.create!(name: 'Test')
  end
  let(:membership_test_user) do
    Membership.create!(
      user_id: test_user.id,
      organization_id: organization.id
    )
  end

  let(:organization2) do
    Organization.create!(name: 'Test2')
  end
  let(:membership2_test_user) do
    Membership.create!(
      user_id: test_user.id,
      organization_id: organization2.id
    )
  end

  let(:model_template) do
    ModelTemplate.create!(
      name: 'Test Template',
      description: 'Test Desc',
      max_tokens: 100,
      temperature: 1.0,
      model: 'gpt-4o',
      instruction: 'Chat GPT',
      prompt: 'test',
      placeholder: 'reference',
      organization_id: organization.id,
      user: test_user
    )
  end

  let(:openai_service) { instance_double(OpenaiService) }

  def delete_model_templates(id, user)
    delete "/v1/model_templates/#{id}", {}, as_user(user)
  end

  describe 'DELETE model template' do
    before do
      # init data
      organization
      test_user
      membership_test_user
      model_template
    end

    context 'when not from the same organization' do
      before do
        organization2
        model_template.update!(organization_id: organization2.id)
      end

      it 'return unauthorized' do
        delete_model_templates(model_template.id, test_user)

        expect_response(:forbidden)
      end
    end

    context 'when does not have any related template variable, file, model and assistant' do
      it 'discard model template properly' do
        delete_model_templates(model_template.id, test_user)

        expect_response(:ok)

        model_template.reload
        expect(model_template.discarded_at.nil?).to be_falsey
      end
    end

    context 'when file present' do
      let!(:openai_file) do
        OpenaiFile.create!(
          object_id: model_template.id,
          object_class: model_template.class.name,
          object_class_column: 'reference_output_url',
          openai_file_id: 'file_xxyyzz1234'
        )
      end

      context 'but does not exist in openai' do
        before do
          allow_any_instance_of(OpenaiService).to receive(:delete_file)
                                              .with(openai_file.openai_file_id)
            .and_raise(Faraday::ResourceNotFound)
        end

        it 'discard model template & file properly' do
          delete_model_templates(model_template.id, test_user)

          expect_response(:ok)

          model_template.reload
          openai_file.reload
          expect(model_template.discarded_at.nil?).to be_falsey
          expect(openai_file.discarded_at.nil?).to be_falsey
        end
      end

      context 'and exist in openai' do
        before do
          allow_any_instance_of(OpenaiService).to receive(:delete_file)
                                              .with(openai_file.openai_file_id)
            .and_return({ 'id' => openai_file.openai_file_id })
        end

        it 'discard model template & file properly' do
          delete_model_templates(model_template.id, test_user)

          expect_response(:ok)

          model_template.reload
          openai_file.reload
          expect(model_template.discarded_at.nil?).to be_falsey
          expect(openai_file.discarded_at.nil?).to be_falsey
        end
      end
    end

    context 'when model(s) present' do
      let(:model_with_assistant) do
        Model.create!(
          organization_id: organization.id,
          name: 'test model1',
          instruction: 'test',
          temperature: 1.0,
          max_tokens: 100,
          model_template_id: model_template.id,
          openai_assistant_id: 'asst_abc'
        )
      end

      let(:model_without_assistant) do
        Model.create!(
          organization_id: organization.id,
          name: 'test model2',
          instruction: 'test',
          temperature: 1.0,
          max_tokens: 100,
          model_template_id: model_template.id,
          openai_assistant_id: nil
        )
      end

      context 'and model has assistant' do
        before do
          model_with_assistant
        end

        context 'but does not exist in openai' do
          before do
            allow_any_instance_of(OpenaiService).to receive(:retrieve_assistant)
                                                .with(model_with_assistant.openai_assistant_id)
              .and_raise(Faraday::ResourceNotFound)

            allow_any_instance_of(OpenaiService).to receive(:delete_assistant)
                                                .with(model_with_assistant.openai_assistant_id)
              .and_raise(Faraday::ResourceNotFound)
          end

          it 'discard models & model template properly' do
            delete_model_templates(model_template.id, test_user)

            expect_response(:ok)

            model_template.reload
            model_with_assistant.reload
            expect(model_template.discarded_at.nil?).to be_falsey
            expect(model_with_assistant.discarded_at.nil?).to be_falsey
          end
        end

        context 'and exist in openai' do
          before do
            allow_any_instance_of(OpenaiService).to receive(:retrieve_assistant)
                                                .with(model_with_assistant.openai_assistant_id)
              .and_return({ 'id' => model_with_assistant.openai_assistant_id })

            allow_any_instance_of(OpenaiService).to receive(:delete_assistant)
                                                .with(model_with_assistant.openai_assistant_id)
              .and_return({ 'id' => model_with_assistant.openai_assistant_id })
          end

          it 'discard models & model template properly' do
            delete_model_templates(model_template.id, test_user)

            expect_response(:ok)

            model_template.reload
            model_with_assistant.reload
            expect(model_template.discarded_at.nil?).to be_falsey
            expect(model_with_assistant.discarded_at.nil?).to be_falsey
          end

          context 'with vector store' do
            let!(:openai_file) do
              OpenaiFile.create!(
                object_id: model_template.id,
                object_class: model_template.class.name,
                object_class_column: 'reference_output_url',
                openai_file_id: 'file_xxyyzz1234'
              )
            end

            before do
              allow_any_instance_of(OpenaiService).to receive(:retrieve_assistant)
                .with(model_with_assistant.openai_assistant_id)
                .and_return(
                  {
                    'id' => model_with_assistant.openai_assistant_id,
                    'tool_resources' => {
                      'file_search' => {
                        'vector_store_ids' => ['vs_xyz1234']
                      }
                    }
                  }
                )

              allow_any_instance_of(OpenaiService).to receive(:list_vector_store_files)
                .with('vs_xyz1234')
                .and_return(
                  {
                    'id' => 'vs_xyz1234',
                    'data' => [
                      {
                        'id' => 'file_xxyyzz1234'
                      }
                    ]
                  }
                )

              allow_any_instance_of(OpenaiService).to receive(:delete_vector_store)
                .with('vs_xyz1234')
                .and_return({ 'id' => 'vs_xyz1234' })
            end

            it 'discard file, models & model template properly' do
              delete_model_templates(model_template.id, test_user)

              expect_response(:ok)

              model_template.reload
              model_with_assistant.reload
              openai_file.reload
              expect(model_template.discarded_at.nil?).to be_falsey
              expect(model_with_assistant.discarded_at.nil?).to be_falsey
              expect(openai_file.discarded_at.nil?).to be_falsey
            end
          end
        end
      end

      context 'and model does not have assistant' do
        before do
          model_without_assistant
        end

        it 'discard models & model template properly' do
          delete_model_templates(model_template.id, test_user)

          expect_response(:ok)

          model_template.reload
          model_without_assistant.reload
          expect(model_template.discarded_at.nil?).to be_falsey
          expect(model_without_assistant.discarded_at.nil?).to be_falsey
        end
      end
    end

    context 'when model template variable present' do
      let!(:variable_with_file) do
        ModelTemplateVariable.create!(
          model_template_id: model_template.id,
          name: 'File1',
          description: 'File1',
          variable_reference_url: 'http://localhost/images/hello_world.png'
        )
      end

      let!(:variable_openai_file) do
        OpenaiFile.create!(
          object_id: variable_with_file.id,
          object_class: variable_with_file.class.name,
          object_class_column: 'variable_reference_url',
          openai_file_id: 'file_xxyyzz1234'
        )
      end

      let!(:variable_without_file) do
        ModelTemplateVariable.create!(
          model_template_id: model_template.id,
          name: 'Tone',
          description: 'Desc'
        )
      end

      it 'discard template variables & model template properly' do
        delete_model_templates(model_template.id, test_user)

        expect_response(:ok)

        model_template.reload
        variable_with_file.reload
        variable_openai_file.reload
        variable_without_file.reload
        expect(model_template.discarded_at.nil?).to be_falsey
        expect(variable_with_file.discarded_at.nil?).to be_falsey
        expect(variable_openai_file.discarded_at.nil?).to be_falsey
        expect(variable_without_file.discarded_at.nil?).to be_falsey
      end
    end
  end
end

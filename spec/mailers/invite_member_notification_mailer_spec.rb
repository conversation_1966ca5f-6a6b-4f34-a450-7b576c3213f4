# frozen_string_literal: true

require 'rails_helper'

RSpec.describe InviteMemberNotificationMailer, type: :mailer do
  let!(:organization) do
    Organization.create!(name: 'Test')
  end

  let!(:admin) do
    User.create!(
      display_name: 'Admin',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:membership) do
    Membership.create!(
      user_id: admin.id,
      organization_id: organization.id,
      role: 1
    )
  end

  let!(:organization_team) do
    OrganizationTeam.create!(
      name: 'Test',
      organization_id: organization.id
    )
  end

  let!(:user_invitation) do
    UserInvitation.create!(
      email: '<EMAIL>',
      invitation_status: 'invited',
      invitation_code: 'test',
      invitation_expiry_date: Time.current + 7.days,
      organization_id: organization.id,
      invited_by_membership_id: membership.id,
      role: 1,
      invited_to_organization_team_id: organization_team.id
    )
  end

  let!(:mail) do
    mail_object = nil

    allow(InviteMemberNotificationMailer).to receive(:with).and_wrap_original do |obj, *args|
      mail_object = obj.call(*args)
    end

    ::Mailer::InviteMemberNotificationMailerJob.new.perform(user_invitation.id)
    mail_object.send_email
  end

  it 'renders email correctly' do
    expect(mail.subject).to eq("You've Invited a New Team Member to TuneAI")
    expect(mail.to).to eq(['<EMAIL>'])
    expect(mail.from).to eq(['<EMAIL>'])
    expect(mail.body.encoded).to include("You've successfully invited testuser1 to join your team on TuneAI")
    expect(mail.body.encoded).to include('http://localhost:3000/settings/organization/members')
    expect(mail.body.encoded).to include('Best regards,')
    expect(mail.body.encoded).to include('The TuneAI Team')
    expect(mail.body.encoded).to include('BrandRev.ai')
  end
end

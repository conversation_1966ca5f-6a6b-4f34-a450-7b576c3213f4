# frozen_string_literal: true

module V1
  class PlatformRoleOutputs < ApiOutput
    def format
      {
        id: @object.id,
        email: @object.email,
        role: @object.role,
        user: {
          id: @object.user.id,
          name: @object.user.display_name,
          display_name: @object.user.display_name,
          photo_url: @object.user.photo_url,
          email: @object.user.email
        },
        organization_team: @object.organization_team
      }
    end
  end
end

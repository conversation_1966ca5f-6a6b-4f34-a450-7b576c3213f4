# frozen_string_literal: true

module V1
  class MembershipOutput < ApiOutput
    def format
      {
        id: @object.id,
        invited_email: @object.invited_email,
        role: @object.membership_role,
        user: user_output,
        organization_team: organization_team_output
      }
    end

    def user_output
      return {} if @object.user.nil?

      # Only use nano_format to avoid recursion
      V1::UserOutput.new(@object.user).nano_format
    end

    def organization_team_output
      return if @object.organization_team_ids.blank? || organization_teams.blank?

      curr_org_teams = organization_teams.select { |ot| @object.organization_team_ids.include?(ot.id) }

      return unless curr_org_teams.present?

      curr_org_teams.map do |curr_org_team|
        {
          id: curr_org_team.id,
          name: curr_org_team.name
        }
      end
    end

    def users
      @options[:users]
    end

    def organization_teams
      @options[:organization_teams]
    end
  end
end

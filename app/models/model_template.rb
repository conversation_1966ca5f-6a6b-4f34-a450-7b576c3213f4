class ModelTemplate < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }

  belongs_to :organization
  belongs_to :user
  belongs_to :template_category, optional: true
  belongs_to :organization_team, optional: true
  has_many :models, dependent: :destroy
  has_many :model_template_variables, dependent: :destroy
  has_many :model_template_ins, dependent: :destroy
  has_many :model_ratings, dependent: :destroy

  validates :name, presence: true
  validates :max_tokens, presence: true, numericality: { only_integer: true, greater_than: 0 }
  validates :temperature, presence: true, numericality: { greater_than_or_equal_to: 0, less_than_or_equal_to: 2 }
  validates :model, presence: true
  validates :instruction, presence: true
  validates :prompt, presence: true

  scope :organization_prompts, -> { where(organization_prompt: true) }
  scope :team_templates, -> { where.not(organization_team_id: nil) }
  scope :personal_templates, -> { where(organization_prompt: false, organization_team_id: nil) }

  belongs_to :test_prompt_chat, optional: true, class_name: 'Chat', foreign_key: 'test_prompt_chat_id'
  belongs_to :workspaces_membership, optional: true

  enum template_type: string_enum('default', 'expert')

  # validate :valid_test_prompt_chat_id

  # def valid_test_prompt_chat_id
  #   valid = self.test_prompt_chat_id.nil? || Chat.exists?(id: self.test_prompt_chat_id)

  #   self.errors.add :base, 'Test prompt chat does not exist' if !valid
  # end
end

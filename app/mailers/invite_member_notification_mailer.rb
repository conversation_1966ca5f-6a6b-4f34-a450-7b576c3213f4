class InviteMemberNotificationMailer < ApplicationMailer
  default from: ENV['MAILER_EMAIL_ADDRESS']

  def send_email
    @user_invitation = params[:user_invitation]
    @user_name = @user_invitation.email.split('@').first
    @invited_by_user = @user_invitation.invited_by_membership.user
    @organization = @user_invitation.organization
    @base_app = ENV['WEBAPP']
    @cs_email = ENV['CUSTOMER_SERVICE_EMAIL_ADDRESS']
    @member_setting_app_path = "#{@base_app}/settings/organization/members"

    mail(to: @user_invitation.email, subject: "You've Invited a New Team Member to TuneAI")
  end
end

# frozen_string_literal: true

module V1
  class PlatformRolesController < ApiController
    authorize_auth_token! :all

    def list_admin_platform_roles
      admins_list = service.list_admin_platform_roles
      render_json_array admins_list, use: :format, status: :ok
    end

    private

    def default_output
      ::V1::PlatformRoleOutputs
    end

    def service
      @service ||= ::PlatformRoleService.new(current_user)
    end
  end
end

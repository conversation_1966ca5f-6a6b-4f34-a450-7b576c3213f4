# frozen_string_literal: true

require_relative '../../services/llm_providers'

module V1
  class OpenaiController < ApiController
    include ActionController::Live

    authorize_auth_token! :all

    ### disabled, outdated logic
    # def stream_response
    #   input = ::V1::ChatCompletionInput.new(request_body)
    #   validate! input, capture_failure: true

    #   output = input.output

    #   chat_params = service.initialize_chat(input.output, output[:chat_id])

    #   response.headers['Content-Type'] = 'text/event-stream'
    #   response.headers['Last-Modified'] = Time.now.httpdate

    #   sse = SSE.new(response.stream)

    #   service.stream_response(sse, chat_params)
    # ensure
    #   sse&.close
    #   service.update_remaining_tokens(chat_params)
    # end

    def stream_response_v2
      input = ::V2::ChatCompletionInput.new(request_body)
      validate! input, capture_failure: true

      chat_params = service.initialize_chat_v2(input.output)

      response.headers['Content-Type'] = 'text/event-stream'
      response.headers['Last-Modified'] = Time.now.httpdate

      sse = SSE.new(response.stream)
      service.stream_response_v2(sse, chat_params)
    ensure
      sse&.close
      service.update_remaining_tokens_v2(chat_params)
    end

    def chat_stream_v2
      input = ::V2::ChatStreamInput.new(request_body)
      validate! input, capture_failure: true

      output = input.output
      chat_params = service.initialize_chat_v2(output)

      response.headers['Content-Type'] = 'text/event-stream'
      response.headers['Last-Modified'] = Time.now.httpdate

      sse = SSE.new(response.stream)

      service.stream_response_v2(sse, chat_params)
    ensure
      sse&.close
      service.update_remaining_tokens_v2(chat_params)
    end

    def show_workspace
      workspace = service.show_chat(params[:workspace_id])

      render_json workspace, ::V1::WorkspaceOutput, use: :format
    end

    private

    def default_output
      ::V1::MessageOutput
    end

    def service
      chat = Chat.find(params[:chat_id]) if params[:chat_id]
      model_name = chat&.model&.model || 'openai/gpt-4o-mini'
      @service ||= ::LLMProviders.get_service(current_user, model_name)
    end
  end
end

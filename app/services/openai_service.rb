# frozen_string_literal: true

require 'tiktoken_ruby'

class OpenaiService < BaseLlmService
  GPT4O_TOKEN_CONVERSION_RATE_INPUT = 1
  GPT4O_TOKEN_CONVERSION_RATE_OUTPUT = 3
  GPT4OMINI_TOKEN_CONVERSION_RATE_INPUT = 0.03
  GPT4OMINI_TOKEN_CONVERSION_RATE_OUTPUT = 0.12

  def initialize(user)
    @user = user
    @client = OpenAI::Client.new(access_token: Rails.application.credentials.openai_key)
  end

  def initialize_chat_v2(params)
    user_prompt = params[:query].to_s
    chat_id = params[:chat_id]
    params[:chat_type] = 'general'
    file_url = params[:file_url].to_s
    image_url = params[:image_url].to_s
    source_model_template_id = params[:source_model_template_id]
    query_list = params[:query_list]
    params[:organization_id]
    message_type = if file_url.present? && image_url.present?
                     'image_and_file'
                   elsif file_url.present?
                     'file_search'
                   elsif image_url.present?
                     'image'
                   else
                     'general'
                   end

    test_prompt_params = as_boolean(params[:test_prompt])
    test_prompt = source_model_template_id.present? && test_prompt_params
    authorize! test_prompt if test_prompt_params

    regenerate_chat = as_boolean(params[:regenerate])

    if chat_id
      # TODO: verify chat ownership
      chat = ::Chat.find_by!(id: chat_id)
    else
      chat_service = ChatService.new(@user)
      chat = chat_service.create(params)
    end

    if test_prompt
      template = ModelTemplate.find(source_model_template_id)
      template.update(test_prompt_chat_id: chat.id)
    end

    org_credits = remaining_tokens_v2(params)

    authorize! org_credits.positive?, on_error: 'Token insufficient, contact admin to topup token!'

    tokens = token_count_v2(user_prompt)
    model = chat.model
    total_tokens = token_count_v2(model.instruction) + tokens
    total_credits = token_to_credit(model.model, total_tokens, 'user')

    authorize! total_credits <= org_credits, on_error: 'Token insufficient, contact admin to topup token!'
    authorize! total_credits <= model.max_tokens.to_i,
               on_error: 'Prompt messages are exceeding the maximum allowed tokens'

    message_data = {
      chat_id: chat.id,
      sender: 'user',
      content: '',
      model_used: chat.model.model
    }
    user_messages = {}

    if regenerate_chat
      # TODO: save openai message id, then when regenerate
      # discard openai message from related thread
      # reason: to conserve token usage

      last_user_message = Message.where(chat_id: chat.id, sender: 'user',
                                        status_on_thread: 'present').order(:created_at).last
      last_assistant_message = Message.where(chat_id: chat.id, sender: 'assistant',
                                             status_on_thread: 'present').order(:created_at).last

      authorize! last_user_message.present?, on_error: 'There is no chat history'

      user_prompt = 'Refine your answer'

      last_assistant_message.update(status_on_thread: 'removed')
      message_data[:status_on_thread] = 'hidden'
    end

    # multi input with model template
    if chat.source_model_template_id.present? && query_list.present?
      begin
        query_list = JSON.parse(query_list)
      rescue JSON::ParserError
        authorize! false, on_error: 'Input invalid'
      rescue TypeError
        query_list = query_list
      end

      authorize! !query_list.blank?, on_error: 'Input invalid'

      message_type = 'multi_input'

      input_ids = query_list.keys
      input_req = ::ModelTemplateIn.where(id: input_ids, model_template_id: chat.source_model_template_id)
      authorize! input_req.count == input_ids.size, on_error: 'Not all Input filled'

      message_file_ids = []
      file_urls = []
      image_urls = []
      user_prompt = ''
      message_data_content = []

      input_ids.each do |input_id|
        data_input = query_list[input_id]
        query_input = data_input['query']
        file_input = data_input['file_url']
        image_input = data_input['image_url']

        authorize! !query_input.blank?, on_error: 'Input invalid'

        curr_in = input_req.find { |i| i.id == input_id.to_i }
        curr_message_data_content = "**#{curr_in.name}**:\n\n#{query_input}"

        user_prompt += "'input_id#{input_id}' => #{query_input}"
        unless image_input.blank?
          image_urls << image_input

          user_prompt += ", supplement image: '#{image_input}'"
          curr_message_data_content += ", '#{image_input}'"
        end

        unless file_input.blank?
          file_urls << file_input
          message_file = create_file(file_input, 'assistants')

          message_file_ids << message_file['id']
          user_prompt += ", supplement file: '#{message_file_ids}'"

          uri = URI.parse(file_input)
          filename = File.basename(uri.path)
          curr_message_data_content += ", '#{remove_hex_identifier(filename)}'"
        end

        user_prompt += ";\n"
        message_data_content << curr_message_data_content
      end

      content = [
        { type: 'text', text: user_prompt }
      ]

      image_urls.each do |image_url|
        content << { type: 'image_url', image_url: { url: image_url } }
      end

      attachments = message_file_ids.map do |file_id|
        { file_id: file_id, tools: [{ type: 'file_search' }] }
      end

      user_messages = {
        role: 'user',
        content: content,
        attachments: attachments
      }

      message_data[:content] = message_data_content.join("\n\n")
      message_data[:image_url] = image_urls.join(' ')
      message_data[:file_url] = file_urls.join(' ')
      message_data[:openai_file_ids] = message_file_ids
    elsif query_list.present?
      begin
        query_list = JSON.parse(query_list)
      rescue JSON::ParserError
        authorize! false, on_error: 'Input invalid'
      rescue TypeError
        query_list = query_list
      end

      authorize! !query_list.blank?, on_error: 'Input invalid'

      input_id = query_list.keys.first
      data_input = query_list[input_id]

      user_prompt = data_input['query']
      file_url = data_input['file_url']
      image_url = data_input['image_url']

      message_type = if file_url.present? && image_url.present?
                       'image_and_file'
                     elsif file_url.present?
                       'file_search'
                     elsif image_url.present?
                       'image'
                     else
                       'general'
                     end
    end

    # TODO: refactor redundant code
    if message_type == 'image_and_file'
      message_file = create_file(file_url, 'assistants')
      attachments = [
        { file_id: message_file['id'], tools: [{ type: 'file_search' }] }
      ]

      user_messages = {
        role: 'user',
        content: [
          { type: 'text', text: user_prompt },
          { type: 'image_url', image_url: { url: image_url } }
        ],
        attachments: attachments
      }

      message_data[:content] = user_prompt
      message_data[:image_url] = image_url
      message_data[:file_url] = file_url
      message_data[:openai_file_ids] = [message_file['id']]
    end

    if message_type == 'file_search'
      message_file = create_file(file_url, 'assistants')
      attachments = [
        { file_id: message_file['id'], tools: [{ type: 'file_search' }] }
      ]

      user_messages = { role: 'user', content: user_prompt, attachments: attachments }

      message_data[:content] = user_prompt
      message_data[:file_url] = file_url
      message_data[:openai_file_ids] = [message_file['id']]
    end

    if message_type == 'image'
      user_messages = {
        role: 'user',
        content: [
          { type: 'text', text: user_prompt },
          { type: 'image_url', image_url: { url: image_url } }
        ]
      }

      message_data[:content] = user_prompt
      message_data[:image_url] = image_url
    end

    if message_type == 'general'
      user_messages = { role: 'user', content: user_prompt }
      message_data[:content] = user_prompt
    end

    openai_file_ids = message_data.delete(:openai_file_ids)
    user_message_obj = ::Message.create!(message_data)

    openai_file_ids&.each do |openai_file_id|
      next unless openai_file_id.present?

      OpenaiFile.create!(
        object_id: user_message_obj.id,
        object_class: user_message_obj.class.name,
        object_class_column: 'file_url',
        openai_file_id: openai_file_id
      )
    end

    assistant_message_obj = ::Message.new(
      chat_id: chat.id,
      sender: 'assistant',
      content: '',
      model_used: chat.model.model
    )

    OpenStruct.new(
      chat: chat,
      model: chat.model,
      user_messages: user_messages,
      assistant_message_obj: assistant_message_obj,
      user_message_obj: user_message_obj,
      save_convo: true,
      prompt_messages: [user_messages]
    )
  end

  def stream_response_v2(sse, params)
    chat = params.chat
    model = params.model
    user_messages = params.user_messages
    assistant_message_obj = params.assistant_message_obj
    user_message_obj = params.user_message_obj
    openai_assistant_id = model.openai_assistant_id

    openai_chat = OpenaiChat.find_by(chat_id: chat.id)

    if openai_chat
      assistant_id = openai_assistant_id || openai_chat.openai_assistant_id
      thread_id = openai_chat.openai_thread_id

      if thread_id.nil?
        thread_id = @client.threads.create['id']

        openai_chat.update(
          openai_thread_id: thread_id
        )
      end

      @client.messages.create(
        thread_id: thread_id,
        parameters: user_messages
      )

      @client.runs.create(
        thread_id: thread_id,
        parameters: {
          assistant_id: assistant_id,
          stream: proc do |chunk, _bytesize|
            if chunk['object'] == 'thread.message.delta'
              print chunk.dig('delta', 'content', 0, 'text', 'value')
              assistant_message_obj.content = assistant_message_obj.content + chunk.dig('delta', 'content', 0, 'text',
                                                                                        'value').to_s
              sse.write(chunk.merge(chat_id: chat.id))
            end

            if chunk['object'] == 'thread.run' && chunk['status'] == 'completed'
              chunk['id']
              usage_data = chunk['usage']
              print("\n", usage_data, "\n")

              user_message_obj.tokens_used = usage_data['prompt_tokens']
              user_message_obj.credits_used = token_to_credit(model.model, usage_data['prompt_tokens'],
                                                              user_message_obj.sender)
              assistant_message_obj.tokens_used = usage_data['completion_tokens']
              assistant_message_obj.credits_used = token_to_credit(model.model, usage_data['completion_tokens'],
                                                                   assistant_message_obj.sender)
            end
          end
        }
      )

      user_message_obj.save!
      assistant_message_obj.save!
      sse.write('[DONE]')
    else
      if openai_assistant_id
        assistant_id = openai_assistant_id
      else
        assistant_id = create_assistant(model)['id']

        model.update(openai_assistant_id: assistant_id)
      end

      thread_id = @client.threads.create['id']

      @client.messages.create(
        thread_id: thread_id,
        parameters: user_messages
      )

      @client.runs.create(
        thread_id: thread_id,
        parameters: {
          assistant_id: assistant_id,
          stream: proc do |chunk, _bytesize|
            if chunk['object'] == 'thread.message.delta'
              print chunk.dig('delta', 'content', 0, 'text', 'value')
              assistant_message_obj.content = assistant_message_obj.content + chunk.dig('delta', 'content', 0, 'text',
                                                                                        'value').to_s
              sse.write(chunk.merge(chat_id: chat.id))
            end

            if chunk['object'] == 'thread.run' && chunk['status'] == 'completed'
              chunk['id']
              usage_data = chunk['usage']
              print("\n", usage_data, "\n")

              user_message_obj.tokens_used = usage_data['prompt_tokens']
              user_message_obj.credits_used = token_to_credit(model.model, usage_data['prompt_tokens'],
                                                              user_message_obj.sender)
              assistant_message_obj.tokens_used = usage_data['completion_tokens']
              assistant_message_obj.credits_used = token_to_credit(model.model, usage_data['completion_tokens'],
                                                                   assistant_message_obj.sender)
            end
          end
        }
      )

      OpenaiChat.create(
        chat_id: chat.id,
        openai_thread_id: thread_id,
        openai_assistant_id: assistant_id
      )

      user_message_obj.save!
      assistant_message_obj.save!
      sse.write('[DONE]')
    end
  end

  def update_remaining_tokens_v2(params)
    return if params.nil?

    organization = if params[:organization_id].present?
                     Organization.find(params[:organization_id])
                   else
                     @user.membership.organization
                   end

    org_plan = organization.organizations_plans_thresholds.first
    purchased_credits = org_plan.purchased_credits
    monthly_credits = org_plan.remaining_monthly_credits
    prev_purchased_credits = purchased_credits
    prev_monthly_credits = monthly_credits

    assistant_message = params.assistant_message_obj
    user_message = params.user_message_obj

    credits_used = (assistant_message.credits_used || 0) + (user_message.credits_used || 0)

    # Deduct from monthly credits first until 0
    # then from purchased credits if not enough
    # then again from monthly credits until negative if it still not enough
    monthly_deducted = false
    if monthly_credits > 0
      monthly_credits -= credits_used
      monthly_deducted = true
    end

    if monthly_credits <= 0
      credits_used_left = 0
      if monthly_deducted
        credits_used_left = monthly_credits.abs
        monthly_credits = 0
      else
        credits_used_left = credits_used
      end

      purchased_credits -= credits_used_left

      if purchased_credits < 0
        monthly_credits += purchased_credits
        purchased_credits = 0
      end
    end

    current_time = Time.current
    CreditHistory.create!(
      organization_id: organization.id,
      action: 'org_chat_stream',
      monthly_credits: -(prev_monthly_credits - monthly_credits),
      purchased_credits: -(prev_purchased_credits - purchased_credits),
      action_at: current_time,
      user_id: @user.id,
      message_ids: [assistant_message&.id, user_message&.id].compact
    )

    org_plan.update(
      purchased_credits: purchased_credits,
      remaining_monthly_credits: monthly_credits
    )
  end

  def create_assistant(model, **options)
    tools = assistant_tools_builder(model)

    parameters = {
      model: model.model,
      name: "#{model.model_template_id}: #{model.name}",
      temperature: model.temperature,
      description: '',
      instructions: model.instruction,
      tools: tools
    }

    parameters[:tool_resources] = options[:tool_resources] if options[:tool_resources].present?

    @client.assistants.create(
      parameters: parameters
    )
  end

  def modify_assistant(model, **options)
    tools = assistant_tools_builder(model)
    assistant_id = model.openai_assistant_id

    parameters = {
      model: model.model,
      name: "#{model.model_template_id}: #{model.name}",
      temperature: model.temperature,
      description: '',
      instructions: model.instruction,
      tools: tools
    }

    parameters[:tool_resources] = options[:tool_resources] if options[:tool_resources].present?

    @client.assistants.modify(
      id: assistant_id,
      parameters: parameters
    )
  end

  def retrieve_assistant(assistant_id)
    @client.assistants.retrieve(id: assistant_id)
  end

  def delete_assistant(assistant_id)
    @client.assistants.delete(id: assistant_id)
  end

  def create_assistant_files(vector_store_id, **options)
    mode = options[:mode]

    return unless vector_store_id.present? && %w[openai_file_ids file_urls].include?(mode)

    openai_file_ids = []
    purpose = 'assistants'

    if mode == 'file_urls'
      file_urls = options[:file_urls]
      model = options[:model]
      file_urls.each do |url|
        response_file = create_file(url, purpose)
        openai_file_ids << response_file['id']

        OpenaiFile.create!(
          object_id: model.id,
          object_class: model.class.name,
          object_class_column: 'openai_assistant_id',
          openai_file_id: response_file['id']
        )
      end
    elsif mode == 'openai_file_ids'
      openai_file_ids = options[:openai_file_ids]
    end

    @client.vector_store_file_batches.create(
      vector_store_id: vector_store_id,
      parameters: {
        file_ids: openai_file_ids
      }
    )
  end

  def create_vector_store(name)
    @client.vector_stores.create(
      parameters: {
        name: name
      }
    )
  end

  def delete_vector_store(vector_store_id)
    response_vector_store_files = list_vector_store_files(vector_store_id)
    openai_file_ids = response_vector_store_files['data'].collect { |file| file['id'] }

    openai_file_ids.each do |f_id|
      delete_file(f_id)
    end

    @client.vector_stores.delete(id: vector_store_id)
  end

  def list_vector_store_files(vector_store_id)
    @client.vector_store_files.list(vector_store_id: vector_store_id)
  end

  def create_file(file_url, purpose)
    filename = File.basename(URI.parse(file_url).path)
    root_filepath = Rails.root.join('tmp', filename)
    File.open(root_filepath, 'wb') do |fo|
      fo.write(URI.open(file_url).read)
    end

    open_file = File.open(root_filepath)

    response = @client.files.upload(parameters: { file: open_file, purpose: purpose })

    File.delete(root_filepath)

    response
  end

  def handle_file_search(file_url, user_prompt, message_data, user_messages)
    message_file = create_file(file_url, 'assistants')
    attachments = [
      { file_id: message_file['id'], tools: [{ type: 'file_search' }] }
    ]

    user_messages.merge!(
      role: 'user',
      content: user_prompt,
      attachments: attachments
    )

    message_data[:content] = user_prompt
    message_data[:file_url] = file_url
    message_data[:openai_file_ids] = [message_file['id']]
  end

  def delete_file(openai_file_id)
    @client.files.delete(id: openai_file_id)
  end

  private

  def initialize_client
    OpenAI::Client.new(
      access_token: Rails.application.credentials.openai_key
    )
  end

  def remaining_tokens(chat)
    model = chat.model
    organization = model.organization
    org_plan = organization.organizations_plans_thresholds.first

    org_plan.purchased_credits + org_plan.remaining_monthly_credits
  end

  def remaining_tokens_v2(params)
    organization = if params[:organization_id].present?
                     Organization.find(params[:organization_id])
                   else
                     @user.membership.organization
                   end

    org_plan = organization.organizations_plans_thresholds.first

    org_plan.purchased_credits + org_plan.remaining_monthly_credits
  end

  def token_count_v2(text)
    OpenAI.rough_token_count(text)
  end

  def assistant_tools_builder(model)
    tools = []

    tools << { type: 'file_search' } if model.file_search

    tools << { type: 'code_interpreter' } if model.code_interpreter

    tools
  end

  def remove_hex_identifier(filename)
    arr_filename = filename.split('.')

    if arr_filename.size > 1
      no_ext_filename = arr_filename[..(arr_filename.size - 2)].join('.')
      ext = arr_filename.last

      arr_no_ext_filename = no_ext_filename.split('-')
      if arr_no_ext_filename.size > 1
        no_hex_filename = arr_no_ext_filename[..(arr_no_ext_filename.size - 2)].join('-')

        filename = [no_hex_filename, ext].join('.')
      end
    end

    filename
  end

  def token_to_credit(model, tokens, sender)
    used_conversion = ''

    if model == 'gpt-4o'
      used_conversion += 'OpenaiService::GPT4O_TOKEN_CONVERSION_RATE'
    elsif model == 'gpt-4o-mini'
      used_conversion += 'OpenaiService::GPT4OMINI_TOKEN_CONVERSION_RATE'
    end

    if sender == 'user'
      used_conversion += '_INPUT'
    elsif sender == 'assistant'
      used_conversion += '_OUTPUT'
    end

    rates = used_conversion.constantize
    (tokens * rates).ceil(9)
  end
end

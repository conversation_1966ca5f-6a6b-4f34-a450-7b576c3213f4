# frozen_string_literal: true

require 'tiktoken_ruby'
require 'metainspector'

class OpenrouterService < BaseLlmService
  # Define token conversion rates for different models
  GPT4_TOKEN_CONVERSION_RATE_INPUT = 1
  GPT4_TOKEN_CONVERSION_RATE_OUTPUT = 3
  GPT4OMINI_TOKEN_CONVERSION_RATE_INPUT = 0.03
  GPT4OMINI_TOKEN_CONVERSION_RATE_OUTPUT = 0.12
  DEEPSEEK_TOKEN_CONVERSION_RATE_INPUT = 0.03
  DEEPSEEK_TOKEN_CONVERSION_RATE_OUTPUT = 0.12
  GEMINI_TOKEN_CONVERSION_RATE_INPUT = 0.03
  GEMINI_TOKEN_CONVERSION_RATE_OUTPUT = 0.12
  MAX_RESULTS = 5

  def initialize(user)
    @user = user
    @client = initialize_client
  end

  def stream_response_v2(sse, params)
    @chat = params.chat
    @model = params.model
    @user_messages = params.user_messages
    @assistant_message_obj = params.assistant_message_obj
    @user_message_obj = params.user_message_obj
    @web_search = params[:web_search]
    @annotations = []

    stream_chat_response(sse)
    save_messages
    sse.write('[DONE]')
  end

  def handle_file_search(file_url, user_prompt, message_data, user_messages)
    enhanced_prompt = "#{user_prompt}\n\nFile URL: #{file_url}"

    user_messages.merge!(
      role: 'user',
      content: enhanced_prompt
    )

    message_data[:content] = enhanced_prompt
    message_data[:file_url] = file_url
  end

  def store_web_search_result(message_id, annotations)
    return unless annotations.present?

    annotations.each do |annotation|
      WebSearchResult.create!(
        message_id: message_id,
        url: annotation[:url],
        title: annotation[:title],
        content: annotation[:content],
        image: annotation[:image],
        site_name: annotation[:site_name] || annotation[:title],
        start_index: annotation[:start_index],
        end_index: annotation[:end_inde]
      )
    end
  end

  private

  def initialize_client
    OpenAI::Client.new(
      access_token: Rails.application.credentials.openrouter_key,
      uri_base: 'https://openrouter.ai/api/v1'
    )
  end

  def token_to_credit(model, tokens, sender)
    used_conversion = ''

    if model.include?('gpt-4')
      used_conversion += 'OpenrouterService::GPT4_TOKEN_CONVERSION_RATE'
    elsif model.include?('gpt-3.5')
      used_conversion += 'OpenrouterService::GPT4OMINI_TOKEN_CONVERSION_RATE'
    elsif model.include?('deepseek')
      used_conversion += 'OpenrouterService::DEEPSEEK_TOKEN_CONVERSION_RATE'
    elsif model.include?('gemini')
      used_conversion += 'OpenrouterService::GEMINI_TOKEN_CONVERSION_RATE'
    end

    if sender == 'user'
      used_conversion += '_INPUT'
    elsif sender == 'assistant'
      used_conversion += '_OUTPUT'
    end

    rates = used_conversion.constantize
    (tokens * rates).ceil(9)
  end

  def get_chat_history(chat, user_messages)
    chat_history = Message.where(chat_id: chat.id, status_on_thread: 'present')
                          .order(:created_at)
                          .pluck(:sender, :content)
                          .map do |sender, content|
                            {
                              role: sender,
                              content: content.to_s # Ensure content is a string
                            }
                          end

    # Add the current user message
    chat_history << user_messages if user_messages.present?

    # Log the chat history for debugging
    Rails.logger.info("Chat history for chat #{chat.id}: #{chat_history.inspect}")

    chat_history
  rescue StandardError => e
    Rails.logger.error("Error getting chat history: #{e.message}")
    [] # Return empty array as fallback
  end

  def determine_content_type(content)
    return 'text' if content.is_a?(String)
    return 'image_url' if content.is_a?(Hash) && content[:type] == 'image_url'
    return 'file' if content.is_a?(Hash) && content[:type] == 'file'
    return 'code' if content.is_a?(Hash) && content[:type] == 'code'
    return 'web_search' if content.is_a?(Hash) && content[:type] == 'web_search'

    'text' # default to text if type is unknown
  end

  def stream_chat_response(sse)
    @client.chat(
      parameters: chat_parameters(sse)
    )
  end

  def chat_parameters(sse)
    {
      stream_options: { include_usage: true },
      plugins: web_search_plugins,
      model: model_name,
      messages: chat_messages,
      stream: proc { |chunk, _bytesize| handle_chunk(chunk, sse) },
      temperature: @model.temperature
    }
  end

  def web_search_plugins
    if @web_search
      [{
        id: 'web',
        max_results: MAX_RESULTS
      }]
    else
      []
    end
  end

  def model_name
    @web_search ? "#{@model.model}:online" : @model.model
  end

  def chat_messages
    if @web_search
      [{ role: 'system', content: search_prompt }] + get_chat_history(@chat, @user_messages)
    else
      get_chat_history(@chat, @user_messages)
    end
  end

  def search_prompt
    <<~PROMPT
      Perform a real-time web search and generate a concise, professional, and accurate summary based on the most relevant sources.

      INSTRUCTIONS:
      - Return exactly #{MAX_RESULTS} result(s).
      - The Summary allign with the annotations.
      - Write in correct markdown.
      - Each sentence in the summary must include at least one inline citation.
      - Use this format for inline citations: [[{number}]](url)
        Example: This is a citation [[1]](url).
    PROMPT
  end

  def handle_chunk(chunk, sse)
    process_annotations(chunk, sse)
    process_content(chunk, sse)
    process_usage(chunk)
  end

  def log_chunk(chunk)
    puts 'chunk'
    puts '--------------------------------'
    puts chunk
    puts '--------------------------------'
    puts 'end chunk'
  end

  def process_annotations(chunk, sse)
    annotations = chunk.dig('choices', 0, 'delta', 'annotations')
    return unless annotations.present?

    extracted_annotations = annotations.map do |annotation|
      if annotation['type'] == 'url_citation'
        citation = annotation['url_citation']

        extracted_web_search_result = extract_web_search_result(citation['url'])

        {
          id: SecureRandom.uuid,
          url: citation['url'],
          title: citation['title'],
          content: citation['content'],
          start_index: citation['start_index'],
          end_index: citation['end_index'],
          image: extracted_web_search_result[:image],
          site_name: extracted_web_search_result[:site_name] || extracted_web_search_result[:title],
          created_at: Time.current
        }
      else
        annotation
      end
    end

    @annotations.concat(extracted_annotations)
    sse.write(format_output(chunk, { type: 'web_search' }))
    store_web_search_results
  end

  def process_content(chunk, sse)
    return unless chunk.dig('choices', 0, 'delta', 'content').present?

    content = chunk.dig('choices', 0, 'delta', 'content')
    @assistant_message_obj.content += content
    sse.write(format_output(chunk, content))
  end

  def validate_citation_format(content)
    # Regular expression to match citations in the format [[number]](url)
    citation_pattern = %r{\[\[(\d+)\]\]\((https?://[^\s)]+)\)}

    # Find all citations in the content
    citations = content.scan(citation_pattern)

    # If no citations found, return original content
    return content if citations.empty?

    # Create a mapping of citation numbers to URLs
    citation_map = citations.to_h

    # Replace any citations that don't match the format
    content.gsub(/\[\[(\d+)\]\]\((.*?)\)/) do |_match|
      number = ::Regexp.last_match(1)
      url = ::Regexp.last_match(2)

      # If the URL is not a valid URL, try to find it in our citation map
      url = citation_map[number] || url unless url.start_with?('http')

      "[[#{number}]](#{url})"
    end
  end

  def format_output(chunk, content)
    content_type = determine_content_type(content)
    {
      id: chunk.dig('id'),
      object: 'thread.message.delta',
      delta: {
        content: [{
          index: 0,
          type: content_type,
          text: if content_type == 'text'
                  { value: content }
                else
                  content_type == 'web_search' ? { value: '' } : content
                end
        }],
        annotations: @annotations
      },
      chat_id: @chat.id,
      message_id: @user_message_obj.id
    }
  end

  def process_usage(chunk)
    return unless chunk.dig('usage').present?

    usage_data = chunk.dig('usage')
    update_message_usage(usage_data)
  end

  def update_message_usage(usage_data)
    @user_message_obj.tokens_used = usage_data['prompt_tokens']
    @user_message_obj.credits_used = calculate_credits(usage_data['prompt_tokens'], @user_message_obj.sender)
    @assistant_message_obj.tokens_used = usage_data['completion_tokens']
    @assistant_message_obj.credits_used = calculate_credits(usage_data['completion_tokens'],
                                                            @assistant_message_obj.sender)
  end

  def calculate_credits(tokens, sender)
    token_to_credit(@model.model, tokens, sender)
  end

  def store_web_search_results
    store_web_search_result(@user_message_obj.id, @annotations) if @annotations.present?
  end

  def save_messages
    # Validate and correct citation format before saving
    if @assistant_message_obj.content.present?
      @assistant_message_obj.content = validate_citation_format(@assistant_message_obj.content)
    end

    @user_message_obj.save!
    @assistant_message_obj.save!
  end

  def extract_web_search_result(_url)
    page = MetaInspector.new(_url)

    puts _url

    {
      image: page.images.best,
      site_name: page.meta_tags.dig('property', 'og:site_name')[0],
      title: page.title

    }
  rescue StandardError
    puts 'Error extracting web search result'
    { image: '', site_name: '' }
  end
end

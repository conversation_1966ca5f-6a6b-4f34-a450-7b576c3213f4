# frozen_string_literal: true

class AppService
  def upload(blob, file, changed_file: false)
    blob.purge if blob.attached? && changed_file
    blob.attach(file) if file
  end

  def authorize!(*permissions, on_error: e('.not_allowed'))
    raise ExceptionHandler::Unauthorized, on_error if permissions.none?
  end

  def assert!(*truths, on_error: e('.invalid'))
    raise Invalid, on_error if truths.none?
  end

  def exist!(object, on_error: e('.none'))
    raise ExceptionHandler::NotFound, on_error if object.blank?

    object
  end

  def duplicate_attributes(record)
    record.attributes
          .with_indifferent_access
          .except(:id, :created_at, :updated_at)
  end

  def as_boolean(value, default: false)
    return default unless value.present?

    ActiveModel::Type::Boolean.new.cast(value)
  end

  def validate_email(email)
    email =~ /\A([^@\s]+)@((?:[-a-z0-9]+\.)+[a-z]{2,})\z/i
  end

  def registered_user(email)
    User.find_by(email: email)
  end

  def authorize_user_roles!(user, roles)
    membership = user.membership
    authorize! membership.present?

    return if roles.blank?

    # Allow admin, super_admin_platform and owner_platform roles to have cross-organization access
    return true if platform_admin?

    authorize! roles.include?(membership.membership_role)

    return unless membership.membership_role == 'super_user'

    authorize! membership.organization&.superuser_privilege
  end

  def verify_user_organization(user)
    membership = user.membership
    organization_id = membership&.organization_id

    authorize! !organization_id.nil?, on_error: 'Not in any organization'

    membership
  end

  def platform_admin?
    return false unless @user&.membership

    %w[admin super_admin_platform owner_platform].include?(@user.membership.membership_role)
  end

  private

  def t(key, options = {})
    key = "actions.#{self.class.name.underscore.tr('/', '.')}#{key}" if key.to_s.first == '.'

    I18n.translate(key, **options.reverse_merge(cascade: true))
  end

  def e(key, options = {})
    opts = options.merge(scope: [:errors].concat(Array(options[:scope])))
    t(key, opts)
  end

  class Invalid < ::StandardError
  end
end

# frozen_string_literal: true

class ModelTemplateService < ::AppService
  def initialize(user)
    @user = user
    @openai_service = OpenaiService.new(@user)
  end

  def index(params)
    if params[:organization_prompt].present?
      params[:organization_prompt] = ActiveModel::Type::Boolean.new.cast(params[:organization_prompt])
    end

    params[:draft] = ActiveModel::Type::Boolean.new.cast(params[:draft]) if params[:draft].present?

    params[:template_type] = Array(params[:template_type]) if params[:template_type]

    templates = ::ModelTemplates.new

    filter = params.slice(
      :template_type,
      :organization_prompt,
      :draft,
      :template_category_id,
      :search,
      :disable_pagination,
      :organization_id
    )

    # Only filter by organization_id if not admin, super_admin_platform, owner_platform and no specific organization_id provided
    unless platform_admin? && params[:organization_id].present?
      filter = filter.merge(
        organization_id: @user.membership.organization_id
      )
    end

    if %w[member team_admin].include?(@user.membership.membership_role)
      org_team_ids = @user.membership.organization_team_ids
      filter_team_ids = [nil] + org_team_ids
      filter = filter.merge(
        organization_team_id: filter_team_ids.uniq
      )
    end

    filtered_result_templates = templates.filter(filter)

    template_ids = filtered_result_templates.pluck(:id)
    variables = ModelTemplateVariable.where(model_template_id: template_ids)
    template_categories = TemplateCategory.where(id: filtered_result_templates.pluck(:template_category_id).compact.uniq)
    number_of_used_times = Message.joins(:chat)
                                  .where(
                                    chats: {
                                      source_model_template_id: template_ids
                                    },
                                    sender: 'user'
                                  )
                                  .group('chats.source_model_template_id')
                                  .count
    ratings = ModelRating.select('model_template_id, COUNT(id) as c, AVG(COALESCE(rating, 0)) as avg')
                         .where(model_template_id: template_ids)
                         .group(:model_template_id)

    organization_teams = OrganizationTeam.where(id: filtered_result_templates.pluck(:organization_team_id).compact.uniq)

    users_with_assigned_team = User.select('users.*, array_agg(m.unnest_org_team_ids) as organization_team_ids_agg')
                                   .joins('LEFT JOIN (SELECT mem.user_id, mem.organization_id, unnest(mem.organization_team_ids) as unnest_org_team_ids FROM memberships mem) AS m ON m.user_id = users.id')
                                   .where(id: filtered_result_templates.pluck(:user_id).compact.uniq)
                                   .group('users.id')

    OpenStruct.new(
      templates: filtered_result_templates,
      variables: variables,
      template_categories: template_categories,
      number_of_used_times: number_of_used_times,
      organization_teams: organization_teams,
      ratings: ratings,
      users_with_assigned_team: users_with_assigned_team
    )
  end

  def show(id)
    template = ModelTemplate.find(id)
    # Allow admin to view any template
    authorize! platform_admin? || template_ownership(template)

    ratings = ModelRating.select('model_template_id, COUNT(id) as c, AVG(COALESCE(rating, 0)) as avg')
                         .where(model_template_id: template.id)
                         .group(:model_template_id)

    OpenStruct.new(
      template: template,
      variables: template.model_template_variables,
      instruction_inputs: template.model_template_ins,
      template_categories: TemplateCategory.where(id: template.template_category_id),
      organization_teams: OrganizationTeam.where(id: template.organization_team_id),
      ratings: ratings
    )
  end

  def create(params)
    is_v2 = as_boolean(params.delete(:v2))

    # verify org membership & roles
    membership = verify_user_organization(@user)
    authorize_user_roles!(@user,
                          %w[super_user owner super_admin team_admin member admin super_admin_platform owner_platform])

    # either organization_prompt or organization_team template, not both
    is_org_template = as_boolean(params[:organization_prompt])
    is_org_team_id_present = params[:organization_team_id].present?

    assert! !(is_org_template && is_org_team_id_present),
            on_error: 'Cannot assign as organization template & team template simultaneously'

    # if organization_team template
    # verify user & organization_team template's assigned team
    if is_org_team_id_present && %w[team_admin member].include?(membership.membership_role)
      authorize! membership.organization_team_ids.compact.present?, on_error: 'You are not assigned to any team!'

      params[:organization_team_id] = ([params[:organization_team_id].to_i] & membership.organization_team_ids).first

      authorize! params[:organization_team_id].present?, on_error: 'You are not assigned to selected team!'
    end

    # setup non-modifiable data
    template = ModelTemplate.new
    # Allow admin, super_admin_platform, and owner_platform to specify organization_id
    organization_id = if params[:organization_id].present? && platform_admin?
                        params[:organization_id]
                      else
                        @user.membership.organization_id
                      end
    params[:organization_id] = organization_id
    params[:user_id] = @user.id

    if params[:template_category_id].present?
      template_category = TemplateCategory.find(params[:template_category_id])
      authorize! platform_admin? || template_category_ownership(template_category)
    end

    if params[:organization_team_id].present?
      org_team = OrganizationTeam.find(params[:organization_team_id])
      authorize! platform_admin? || organization_team_ownership(org_team)
    end

    ActiveRecord::Base.transaction do
      template = ModelTemplate.create!(params)

      result_rules = build_rules_from_template(template)

      unless is_v2
        # TODO: remove vector store completely, just upload directly when thread created to save cost
        # create vector store
        vs_response = @openai_service.create_vector_store("Vector Store Template: #{template.name}-#{template.id}")
        tool_resources = {
          file_search: {
            vector_store_ids: [vs_response['id']]
          }
        }

        # Upload file to OpenAI assistant if file present
        file_url = template.reference_output_url
        if file_url.present?
          # TODO: v2 will not using vector file openai
          response_file = @openai_service.create_file(file_url, 'assistants') unless is_v2

          OpenaiFile.create!(
            object_id: template.id,
            object_class: template.class.name,
            object_class_column: 'reference_output_url'
          )
          @openai_service.create_assistant_files(
            vs_response['id'],
            mode: 'openai_file_ids',
            openai_file_ids: [response_file['id']]
          )
        end
      end

      list_models = Model.models.values

      # create model
      list_models.each do |m|
        model = Model.create!(
          organization_id: @user.membership.organization_id,
          name: "#{template.name} #{m}",
          model: m,
          instruction: result_rules[:structured_prompt],
          temperature: template.temperature,
          max_tokens: template.max_tokens,
          model_template_id: template.id
        )

        unless is_v2
          assistant_response = @openai_service.create_assistant(model, tool_resources: tool_resources)
          model.update!(openai_assistant_id: assistant_response['id'])
        end
      end
    end

    OpenStruct.new(
      template: template,
      variables: template.model_template_variables,
      template_categories: TemplateCategory.where(id: template.template_category_id)
    )
  end

  def update(id, params)
    is_v2 = as_boolean(params.delete(:v2))

    # verify org membership & roles
    membership = verify_user_organization(@user)
    authorize_user_roles!(@user,
                          %w[super_user owner super_admin team_admin member admin super_admin_platform owner_platform])

    # exclude non-modifiable data
    params.delete(:organization_id) unless platform_admin?
    params.delete(:workspace_id)
    params.delete(:user_id)

    # TODO: Refactor
    # either organization_prompt or organization_team template, not both
    is_org_template = as_boolean(params[:organization_prompt])
    is_org_team_id_present = params[:organization_team_id].present?

    assert! !(is_org_template && is_org_team_id_present),
            on_error: 'Cannot assign as organization template & team template simultaneously'

    # if organization_team template
    # verify user & organization_team template's assigned team
    if is_org_team_id_present && %w[team_admin member].include?(membership.membership_role)
      authorize! membership.organization_team_ids.compact.present?, on_error: 'You are not assigned to any team!'

      params[:organization_team_id] = ([params[:organization_team_id].to_i] & membership.organization_team_ids).first
      authorize! params[:organization_team_id].present?, on_error: 'You are not assigned to selected team!'
    end

    template = ModelTemplate.find(id)
    authorize! platform_admin? || template_ownership(template)
    authorize! platform_admin? || template_user_authority(template, membership)

    if params[:template_category_id].present?
      template_category = TemplateCategory.find(params[:template_category_id])
      authorize! platform_admin? || template_category_ownership(template_category)
    end

    if params[:organization_team_id].present?
      org_team = OrganizationTeam.find(params[:organization_team_id])
      authorize! platform_admin? || organization_team_ownership(org_team)
    end

    current_file_url = template.reference_output_url

    ActiveRecord::Base.transaction do
      template.update!(params)
      result_rules = build_rules_from_template(template)

      list_models = Model.models.values
      models = Model.where(model_template_id: template.id)

      # update model & assistant
      tool_resources = {
        file_search: {
          vector_store_ids: []
        }
      }
      to_be_create_assistant_model = []
      to_be_modify_assistant_model = []

      models.each do |model|
        model.update(
          name: "#{template.name} #{model.model}",
          instruction: result_rules[:structured_prompt],
          temperature: template.temperature,
          max_tokens: template.max_tokens
        )

        next if is_v2

        begin
          assistant_response = @openai_service.retrieve_assistant(model.openai_assistant_id)

          vector_store_id = assistant_response['tool_resources']['file_search']['vector_store_ids'].first

          if vector_store_id.nil?
            to_be_modify_assistant_model << model
          else
            tool_resources = assistant_response['tool_resources'].with_indifferent_access
            @openai_service.modify_assistant(model, tool_resources: tool_resources)
          end
        rescue Faraday::ResourceNotFound
          to_be_create_assistant_model << model
        rescue TypeError
          to_be_create_assistant_model << model
        end
      end

      # Create model if there are model that is not created yet
      not_created_models = list_models - models.pluck(:model).uniq.compact
      unless not_created_models.empty?
        not_created_models.each do |m|
          model = Model.create!(
            organization_id: @user.membership.organization_id,
            name: "#{template.name} #{m}",
            model: m,
            instruction: result_rules[:structured_prompt],
            temperature: template.temperature,
            max_tokens: template.max_tokens,
            model_template_id: template.id
          )

          next if is_v2

          assistant_response = @openai_service.create_assistant(model, tool_resources: tool_resources)
          model.update!(openai_assistant_id: assistant_response['id'])
        end
      end

      result = OpenStruct.new(
        template: template,
        variables: template.model_template_variables,
        template_categories: TemplateCategory.where(id: template.template_category_id)
      )

      return result if is_v2

      # Create vector store, if all previously created model assistant does not have any vector store
      if tool_resources[:file_search][:vector_store_ids].empty?
        vs_response = @openai_service.create_vector_store("Vector Store Template: #{template.name}-#{template.id}")
        tool_resources = {
          file_search: {
            vector_store_ids: [vs_response['id']]
          }
        }
      end

      # Create assistant model if model does not have assistant
      to_be_create_assistant_model.each do |model|
        assistant_response = @openai_service.create_assistant(model, tool_resources: tool_resources)
        model.update!(openai_assistant_id: assistant_response['id'])
      end

      to_be_modify_assistant_model.each do |model|
        @openai_service.modify_assistant(model, tool_resources: tool_resources)
      end

      latest_file_url = template.reference_output_url

      return result if current_file_url == latest_file_url

      openai_file = OpenaiFile.find_by(
        object_id: template.id,
        object_class: template.class.name,
        object_class_column: 'reference_output_url'
      )

      if openai_file.present?
        begin
          response_file = @openai_service.delete_file(openai_file.openai_file_id)
          openai_file.discard! if response_file['deleted']
        rescue Faraday::ResourceNotFound
          openai_file.discard!
        end
      end

      return result if latest_file_url.blank?

      openai_file = OpenaiFile.create!(
        object_id: template.id,
        object_class: template.class.name,
        object_class_column: 'reference_output_url'
      )

      # TODO: error handling
      response_file = @openai_service.create_file(latest_file_url, 'assistants')
      openai_file.update!(openai_file_id: response_file['id'])

      begin
        vector_store_id = tool_resources['file_search']['vector_store_ids'].first
      rescue NoMethodError
        vector_store_id = nil
      end

      @openai_service.create_assistant_files(
        vector_store_id,
        mode: 'openai_file_ids',
        openai_file_ids: [response_file['id']]
      )

      result
    end
  end

  def destroy(id)
    verify_user_organization(@user)
    authorize_user_roles!(@user, %w[super_user owner super_admin team_admin member])

    template = ModelTemplate.find(id)
    authorize! platform_admin? || template_ownership(template)

    ActiveRecord::Base.transaction do
      # Delete associated models and their OpenAI assistants
      template.models.each do |model|
        if model.openai_assistant_id.present?
          begin
            response_assistant = @openai_service.retrieve_assistant(model.openai_assistant_id)
          rescue Faraday::ResourceNotFound
            response_assistant = {}
          end

          begin
            vector_store_id = response_assistant['tool_resources']['file_search']['vector_store_ids'].first
          rescue NoMethodError
            vector_store_id = nil
          rescue TypeError
            vector_store_id = nil
          end

          if vector_store_id
            response_vector_store_files = @openai_service.list_vector_store_files(vector_store_id)
            openai_file_ids = response_vector_store_files['data'].collect { |file| file['id'] }

            @openai_service.delete_vector_store(vector_store_id)
            OpenaiFile.where(openai_file_id: openai_file_ids).update_all(discarded_at: Time.current)
          end

          begin
            @openai_service.delete_assistant(model.openai_assistant_id)
            true
          rescue Faraday::ResourceNotFound
            true
          end
        end
        model.discard!
      end

      # Delete reference output
      OpenaiFile.find_by(
        object_id: template.id,
        object_class: template.class.name,
        object_class_column: 'reference_output_url'
      ).try(:discard!)

      # Delete template variables
      template_variables = ModelTemplateVariable.where(model_template_id: template.id)
      OpenaiFile.where(
        object_id: template_variables.pluck(:id),
        object_class: 'ModelTemplateVariable',
        object_class_column: 'variable_reference_url'
      ).update_all(discarded_at: time_current)
      template_variables.update_all(discarded_at: time_current)

      template.discard!
    end
  end

  def list_comments(id)
    template = ModelTemplate.find(id)
    authorize! platform_admin? || template_ownership(template)

    comments = ModelRating.where(model_template_id: id)

    joins = 'INNER JOIN chats ON messages.chat_id = chats.id' + \
            ' LEFT JOIN workspaces_memberships wm ON wm.workspace_id = chats.workspace_membership_workspace_id AND wm.membership_id = chats.workspace_membership_membership_id' + \
            ' LEFT JOIN memberships m ON m.id = wm.membership_id'

    number_of_used_times = Message.joins(joins)
                                  .where(chats: { source_model_template_id: id }, sender: 'user')
                                  .group('m.user_id')
                                  .count

    if number_of_used_times[nil].present?
      # Create a dummy record to hold used count thats not held by anyone
      other_comment_record = OpenStruct.new(
        id: nil,
        rating: 0,
        user_id: nil,
        model_template_id: id.to_i,
        comment: nil,
        feedback: nil
      )

      comments += [other_comment_record]
    end

    users = User.where(id: comments.pluck(:user_id))
    OpenStruct.new(
      template: template,
      comments: comments,
      number_of_used_times: number_of_used_times,
      users: users
    )
  end

  def duplicate(id)
    membership = verify_user_organization(@user)
    authorize_user_roles!(@user,
                          %w[super_user owner super_admin team_admin member admin super_admin_platform owner_platform])

    base_template = ModelTemplate.find(id)

    if %w[team_admin member].include?(membership.membership_role)
      valid_team = ([base_template.organization_team_id] & membership.organization_team_ids).compact

      authorize! valid_team.present? || base_template.organization_prompt, on_error: 'Invalid team'
    end

    new_template = ModelTemplate.new
    new_variables = []
    new_inputs = []
    template_categories = [base_template.template_category].compact

    ActiveRecord::Base.transaction do
      # Duplicate template
      template_attr = base_template.dup.attributes.compact.slice(
        'name', 'description', 'max_tokens', 'temperature', 'model', 'instruction', 'prompt',
        'placeholder', 'verified', 'organization_prompt', 'draft', 'reference_output_url',
        'organization_id', 'template_category_id', 'organization_team_id'
      )
      template_attr['user_id'] = @user.id
      template_attr['name'] = template_attr['name'] + ' - Copy'

      # Set as personal template when member role duplicating org prompt
      if membership.membership_role == 'member' && base_template.organization_prompt
        template_attr['organization_prompt'] = false
        template_attr['organization_team_id'] = nil
      end

      create_template_result = create(template_attr.with_indifferent_access)
      new_template = create_template_result.template

      new_rules = new_template.prompt

      # Duplicate variables
      variable_service = ModelTemplateVariableService.new(@user)
      base_variables = ModelTemplateVariable.where(model_template_id: base_template.id)
      base_variables.each do |v|
        new_var_attr = v.dup.attributes.compact.slice(
          'name', 'description', 'weight', 'variable_reference_url', 'order'
        )
        new_var_attr['model_template_id'] = new_template.id

        new_var = variable_service.create(new_var_attr.with_indifferent_access)
        new_variables << new_var

        new_rules = new_rules.gsub("<@V#{v.id}>", "<@V#{new_var.id}>")
      end

      # Duplicate inputs
      input_service = ModelTemplateInService.new(@user)
      base_inputs = ModelTemplateIn.where(model_template_id: base_template.id)
      base_inputs.each do |i|
        new_in_attr = i.dup.attributes.compact.slice(
          'name', 'description', 'input_reference_url', 'order'
        )
        new_in_attr['model_template_id'] = new_template.id

        new_in = input_service.create(new_in_attr.with_indifferent_access)
        new_inputs << new_in

        new_rules = new_rules.gsub("<@I#{i.id}>", "<@I#{new_in.id}>")
      end

      update(new_template.id, { prompt: new_rules }.with_indifferent_access)

      new_template.reload
    end

    OpenStruct.new(
      template: new_template,
      variables: new_variables,
      instruction_inputs: new_inputs,
      template_categories: template_categories
    )
  end

  def build_rules_from_template(model_template)
    rules = model_template.prompt
    reference_output = model_template.placeholder
    reference_output_url = model_template.reference_output_url
    reference_output_filename = reference_output_url.present? ? url_to_filename(reference_output_url) : ''

    rules_text = "Now, based on the above reference and the following instructions:\n"
    reference_output_text = "Example 'reference_output':\n"
    final_output_text = "Please generate a response without mentioning any quoted string with @prefix that adheres closely to the format and style of the 'reference_output'"

    variables = ::ModelTemplateVariable.where(model_template_id: model_template.id)
    inputs = ::ModelTemplateIn.where(model_template_id: model_template.id)

    base_inputs_rule = inputs.map do |i|
      "'@input_id#{i.id}'"
    end.join(', ')

    structured_prompt = "With '@input', #{base_inputs_rule} from user message\n\n"

    variables.ids

    # Replace Variables
    hash_variable = {}
    variables.each do |v|
      variable_reference_url = v.variable_reference_url
      value = "'"
      if variable_reference_url.present?
        variable_reference_filename = url_to_filename(variable_reference_url)
        value = "'file: '#{variable_reference_filename}'"
      end

      value = "#{value} description: (#{v.description})".strip if v.description.present?

      value = "(Critical Focus: #{value})" if v.weight == 'high'

      hash_variable["<@V#{v.id}>"] = {
        value: value + "'",
        weight: v.weight
      }
    end
    hash_variable.each do |k, v|
      rules = rules.gsub(k, v[:value])
    end

    # Replace Inputs
    hash_input = {}
    input_explanations = ''
    inputs.each do |v|
      # input_reference_url = v.input_reference_url
      sub_id = "'@input_id#{v.id}'"
      value = "#{v.name}"
      # if input_reference_url.present?
      #   input_reference_filename = url_to_filename(input_reference_url)
      #   value = "file: '#{input_reference_filename}'"
      # end

      value = "#{value}, description: (#{v.description})".strip if v.description.present?

      input_explanations += sub_id + ' => ' + "#{value};\n"

      hash_input["<@I#{v.id}>"] = {
        value: sub_id
      }
    end
    hash_input.each do |k, v|
      rules = rules.gsub(k, v[:value])
    end

    # Replace @input
    rules = rules.gsub('<@VInput>', '@input')
    rules = rules.gsub('<@Vinput>', '@input')

    rules = rules.gsub('<@RefOutput>', "'reference_output'")

    structured_prompt += "and input explanations:\n" + input_explanations + "\n\n"

    structured_prompt += reference_output_text + reference_output + "\n" if reference_output.present?

    structured_prompt += " with file '#{reference_output_filename}'\n" if reference_output_filename.present?

    structured_prompt += rules_text + rules + "\n\n" + final_output_text

    {
      structured_prompt: structured_prompt
    }
  end

  private

  def url_to_filename(url)
    uri = URI.parse(url)
    File.basename(uri.path)
  end

  def template_category_ownership(template_category)
    # Admin, super_admin_platform and owner_platform can access any category
    return true if platform_admin?

    @user.membership&.organization_id == template_category.organization_id
  end

  def template_ownership(template)
    # Admin, super_admin_platform and owner_platform can access any template
    return true if platform_admin?

    # Non-admin users can only access their organization's templates
    @user.membership&.organization_id == template.organization_id
  end

  def organization_team_ownership(org_team)
    # Admin, super_admin_platform and owner_platform can access any team
    return true if platform_admin?

    @user.membership&.organization_id == org_team.organization_id
  end

  def template_user_authority(template, membership)
    return true if platform_admin?

    if membership.membership_role == 'team_admin'
      # true if (either org template OR team template OR own creator) AND
      #   (template have the same team OR template's creator have the same team OR own creator)
      return  (template.organization_prompt || template.organization_team_id.present? || template.user_id == @user.id.to_s) &&
              (
                (template.organization_team_id.present? && membership.organization_team_ids.include?(template.organization_team_id)) ||
                ([template.user&.membership&.organization_team_ids].compact.flatten & membership.organization_team_ids).any? ||
                template.user_id == @user.id.to_s
              )
    elsif membership.membership_role == 'member'
      return  template.user_id == @user.id.to_s
    end

    true
  end

  def time_current
    Time.current
  end
end

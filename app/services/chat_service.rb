# frozen_string_literal: true

require 'tiktoken_ruby'
require_relative 'llm_providers'

class ChatService < ::AppService
  def initialize(user)
    @user = user
  end

  def create(params)
    membership = verify_user_organization(@user)

    if params[:organization_id].present?
      params[:organization_id]
    else
      membership.organization_id
    end

    user_workspace_result = user_workspace_id(params[:workspace_id])

    params[:workspace_id] = user_workspace_result[:workspace_id]
    workspaces_membership = user_workspace_result[:workspaces_membership]

    params[:model] = 'gpt-4o-mini' unless params[:model].present?
    params[:name] = params[:query] unless params[:name].present?

    chat = Chat.new

    ActiveRecord::Base.transaction do
      if params[:source_model_template_id].present?
        model = Model.find_by(model_template_id: params[:source_model_template_id], model: params[:model])
      else
        model = Model
                .where(name: "#{params[:model]}", model: params[:model])
                .then do |query|
                  # if the using openai implementation, then we need to find the assistant id
                  if LLMProviders.provider_for_model(params[:model]) == :openai
                    query.where.not(openai_assistant_id: nil)
                  else
                    query
                  end
                end
                .first

        if model.nil?
          model = Model.create!(
            model: params[:model],
            name: "ChatGPT #{params[:model]}",
            max_tokens: 16_000,
            temperature: 0.8,
            instruction: 'answer as helpful as possible.'
          )

          # Only create assistant for OpenAI models
          if LLMProviders.provider_for_model(params[:model]) == :openai
            llm_service = LLMProviders.get_service(@user, params[:model])
            response_assistant = llm_service.create_assistant(model)
            model.update!(openai_assistant_id: response_assistant['id'])
          end
        end
      end

      chat.name = params[:name]
      chat.compiled_prompt = params[:compiled_prompt]
      chat.workspace_id = params[:workspace_id]
      chat.model_id = model.id
      chat.source_model_template_id = params[:source_model_template_id]
      chat.chat_type = params[:chat_type] || 'general'
      chat.workspace_membership_membership_id = workspaces_membership.membership_id
      chat.workspace_membership_workspace_id = workspaces_membership.workspace_id

      chat.save!

      test_prompt = ActiveModel::Type::Boolean.new.cast(params[:test_prompt])
      if params[:source_model_template_id].present? && test_prompt
        template = ModelTemplate.find(params[:source_model_template_id])
        template.update(test_prompt_chat_id: chat.id)
      end
    end

    chat
  end

  def list(params)
    chats = ::Chats.new

    filter = params.slice(:chat_type, :search, :test_prompt).merge(
      disable_pagination: true,
      ownership: @user&.id
    )

    {
      chats: chats.filter(filter),
      workspace_id: params[:workspace_id]
    }
  end

  def show(id)
    workspace_id = user_workspace_id(id)[:workspace_id]

    membership_id = @user.membership.id

    workspaces_membership_ids = WorkspacesMembership.where(membership_id: membership_id).pluck(:workspace_id)
    Workspace.where(id: workspaces_membership_ids).find(workspace_id)
  end

  def update(chat_id, params)
    membership = verify_user_organization(@user)

    chat = ::Chat.find(chat_id)

    wm = WorkspacesMembership.find_by(workspace_id: chat.workspace_membership_workspace_id,
                                      membership_id: chat.workspace_membership_membership_id)
    authorize! wm&.membership&.user_id == membership.user_id

    if params[:model].present?
      model_string = params.delete(:model)
      model = chat.model

      if model&.model_template_id&.present?
        new_model = Model.find_by(model_template_id: model.model_template_id, model: model_string)
        new_model ||= Model.create!(**model.attributes.compact.slice(
          'max_tokens', 'temperature', 'instruction', 'model_template_id'
        ), name: "#{model.model_template.name} #{model_string}", model: model_string)
      end

      new_model ||= Model.find_by(model: model_string, name: model_string)
      params[:model_id] = new_model.id
    end

    chat.update!(params.compact)
    chat.reload
  end

  private

  def user_workspace_id(id)
    membership = @user.membership
    organization_id = membership.organization_id
    workspace = Workspace.where(organization_id: organization_id).first
    if workspace.nil?
      organization = Organization.find_by(id: organization_id)
      workspace = Workspace.create!(organization_id: organization_id, name: organization.name)
    end

    id = workspace.id
    workspaces_memberships = WorkspacesMembership.where(membership_id: membership.id)
    workspaces_membership_ids = workspaces_memberships.pluck(:workspace_id)

    if workspaces_membership_ids.size.positive?
      return {
        workspace_id: id,
        workspaces_membership: workspaces_memberships.first
      }
    end
    workspaces_membership = WorkspacesMembership.create!(workspace_id: workspace.id, membership_id: membership.id,
                                                         role: 'admin')

    {
      workspace_id: id,
      workspaces_membership: workspaces_membership
    }
  end
end

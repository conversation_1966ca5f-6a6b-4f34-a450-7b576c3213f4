# frozen_string_literal: true

class ModelTemplateVariableService < ::AppService
  def initialize(user)
    @user = user
    @openai_service = OpenaiService.new(@user)
  end

  def create(params)
    is_v2 = as_boolean(params.delete(:v2))

    template = ModelTemplate.find_by(id: params[:model_template_id])
    authorize! template_ownership(template)

    model = Model.where(model_template_id: params[:model_template_id]).first
    exist! model.present?, on_error: 'Model not found'

    template_variable = ModelTemplateVariable.create!(params)
    return template_variable if is_v2

    # Upload file to OpenAI if file present
    file_url = template_variable.variable_reference_url
    if file_url.present?
      assistant_response = @openai_service.retrieve_assistant(model.openai_assistant_id)
      vector_store_id = assistant_response['tool_resources']['file_search']['vector_store_ids'].first
      if vector_store_id.nil?
        vs_response = @openai_service.create_vector_store("Vector Store Template: #{template.name}-#{template.id}")
        vector_store_id = vs_response['id']

        tool_resources = {
          file_search: {
            vector_store_ids: [vs_response['id']]
          }
        }

        @openai_service.modify_assistant(model, tool_resources: tool_resources)
      end

      response_file = @openai_service.create_file(file_url, 'assistants')

      OpenaiFile.create!(
        object_id: template_variable.id,
        object_class: template_variable.class.name,
        object_class_column: 'variable_reference_url',
        openai_file_id: response_file['id']
      )

      @openai_service.create_assistant_files(
        vector_store_id,
        mode: 'openai_file_ids',
        openai_file_ids: [response_file['id']]
      )
    end

    template_variable
  end

  def update(id, params)
    is_v2 = as_boolean(params.delete(:v2))

    template_variable = ModelTemplateVariable.find(id)

    template = template_variable.model_template
    authorize! template_ownership(template)

    model = Model.find_by!(model_template_id: template_variable.model_template_id)

    current_file_url = template_variable.variable_reference_url

    template_variable.update!(params)
    return template_variable if is_v2

    latest_file_url = template_variable.variable_reference_url

    # Update OpenAI file if variable reference is changed
    if current_file_url != latest_file_url
      openai_file = OpenaiFile.find_by(
        object_id: template_variable.id,
        object_class: template_variable.class.name,
        object_class_column: 'variable_reference_url'
      )
      if openai_file.present?
        begin
          response_file = @openai_service.delete_file(openai_file.openai_file_id)
          openai_file.discard! if response_file['deleted']
        rescue Faraday::ResourceNotFound
          openai_file.discard!
        end
      end

      # TODO: error handling
      if latest_file_url.present?
        assistant_response = @openai_service.retrieve_assistant(model.openai_assistant_id)
        vector_store_id = assistant_response['tool_resources']['file_search']['vector_store_ids'].first
        if vector_store_id.nil?
          vs_response = @openai_service.create_vector_store("Vector Store Template: #{template.name}-#{template.id}")
          vector_store_id = vs_response['id']

          tool_resources = {
            file_search: {
              vector_store_ids: [vs_response['id']]
            }
          }

          @openai_service.modify_assistant(model, tool_resources: tool_resources)
        end

        response_file = @openai_service.create_file(latest_file_url, 'assistants')

        OpenaiFile.create!(
          object_id: template_variable.id,
          object_class: template_variable.class.name,
          object_class_column: 'variable_reference_url',
          openai_file_id: response_file['id']
        )

        @openai_service.create_assistant_files(
          vector_store_id,
          mode: 'openai_file_ids',
          openai_file_ids: [response_file['id']]
        )
      end
    end

    template_variable
  end

  def list(query_params)
    model_template_variables = ::ModelTemplateVariables.new

    filter = query_params.slice(
      :model_template_id,
      :search,
      :organization_id
    )

    # Only filter by organization_id if not admin, super_admin_platform, owner_platform and no specific organization_id provided
    unless %w[admin super_admin_platform
              owner_platform].include?(@user.membership.membership_role) && query_params[:organization_id].present?
      filter = filter.merge(
        organization_id: @user.membership&.organization_id
      )
    end

    filtered = model_template_variables.filter(filter)

    OpenStruct.new(
      model_template_variables: filtered
    )
  end

  def destroy(id)
    template_variable = ModelTemplateVariable.find(id)

    template = template_variable.model_template
    authorize! template_ownership(template)

    ActiveRecord::Base.transaction do
      openai_file = OpenaiFile.find_by(
        object_id: template_variable.id,
        object_class: template_variable.class.name,
        object_class_column: 'variable_reference_url'
      )
      if openai_file.present?
        begin
          @openai_service.delete_file(openai_file.openai_file_id)
        rescue Faraday::ResourceNotFound
          {}
        end

        openai_file.discard!
      end
      template_variable.discard!
    end
  end

  private

  def template_ownership(template)
    return true if platform_admin?
    return true if @user.membership&.organization_id == template.organization_id

    false
  end
end

# frozen_string_literal: true

class PlatformRoleService < ::AppService
  def initialize(user)
    @user = user
  end

  def list_admin_platform_roles
    verify_user_organization(@user)
    role = @user.membership.membership_role

    roles_to_include = case role
                       when 'owner_platform'
                         [Membership.role_mappings['super_admin_platform'], Membership.role_mappings['admin'],
                          Membership.role_mappings['owner_platform']]
                       when 'super_admin_platform'
                         [Membership.role_mappings['super_admin_platform'], Membership.role_mappings['admin']]
                       when 'admin'
                         [Membership.role_mappings['admin']]
                       else
                         []
                       end

    authorize! roles_to_include.any?, on_error: 'Not authorized'

    memberships = Membership.includes(:user)
                            .where(role: roles_to_include)
                            .order('users.email')

    return [] if memberships.empty?

    memberships.map do |membership|
      OpenStruct.new(
        id: membership.id,
        email: membership.user.email,
        role: membership.membership_role,
        user: membership.user,
        organization_team: membership.organization_team
      )
    end
  end
end

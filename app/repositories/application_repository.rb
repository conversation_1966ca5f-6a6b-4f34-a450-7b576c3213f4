# frozen_string_literal: true

class ApplicationRepository < ::Repositories::Base
  def available(options = {})
    filter(options.merge(state: 'available'))
  end

  def include?(id)
    @scope.exists?(id)
  end

  def new(params = {})
    @scope.unscoped.new(params)
  end

  def get(id)
    load(@scope.where(id: id).unscope(:limit))[0]
  end

  def name
    @scope.klass.model_name.human
  end

  def limited
    filter(limit: @scope.limit_value)
  end

  def default_limit
    10
  end

  def max_limit
    100
  end

  def as_boolean(value, default: false)
    return default unless value.present?

    ActiveModel::Type::Boolean.new.cast(value)
  end

  private

  def column(name)
    @scope.arel_table[name]
  end

  def filter_by_limit(limit)
    limit = (limit || default_limit).to_i
    limit = [limit, max_limit].min
    @scope.limit(limit)
  end

  module Sortable
    def self.included(base)
      def base.sort_by(column, direction)
        column = column.to_s
        direction = direction.to_s
        raise 'sort direction must be asc/desc' unless %w[asc desc].include?(direction)

        @sort_column = column
        @sort_direction = direction
      end
    end

    private

    def apply_filters(options)
      options[:page] ||= default_page
      paginators = %i[page per_page]

      super(options.except(*paginators))
      super(options.slice(*paginators))
    end

    def default_options
      {
        sort_column: self.class.instance_variable_get('@sort_column'),
        sort_direction: self.class.instance_variable_get('@sort_direction')
      }
    end

    def filter_by_sort_column(sort_column)
      case sort_direction
      when 'asc' then @scope.reorder(order_asc(sort_column))
      when 'desc' then @scope.reorder(order_desc(sort_column))
      end
    end

    def filter_by_sort_direction(direction)
      case direction
      when 'asc' then @scope.reorder(order_asc(sort_column))
      when 'desc' then @scope.reorder(order_desc(sort_column))
      end
    end

    def filter_by_page(page)
      return @scope.except(:limit, :offset) if disable_pagination

      @scope.page(page).per(per_page)
    end

    def order_asc(order_column)
      id_asc = column('id').asc
      return id_asc if order_column.to_s == 'id'

      order_nulls = order_nulls_value || 'NULLS FIRST'
      Arel.sql("#{column(order_column).asc.to_sql} #{order_nulls}, #{id_asc.to_sql}")
    end

    def order_desc(order_column)
      id_desc = column('id').desc
      return id_desc if order_column.to_s == 'id'

      order_nulls = order_nulls_value || 'NULLS LAST'
      Arel.sql("#{column(order_column).desc.to_sql} #{order_nulls}, #{id_desc.to_sql}")
    end

    def order_nulls_value
      order_nulls = @options[:order_nulls]
      return nil if order_nulls.blank?

      if order_nulls == 'first'
        'NULLS FIRST'
      else
        'NULLS LAST'
      end
    end

    def sort_column
      @options[:sort_column]
    end

    def sort_direction
      @options[:sort_direction]
    end

    def disable_pagination
      @options[:disable_pagination]
    end

    def default_page
      1
    end

    def default_per_page
      20
    end

    def per_page
      @options[:per_page] || default_per_page
    end
  end

  include Sortable
end

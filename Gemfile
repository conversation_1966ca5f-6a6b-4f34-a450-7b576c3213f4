source 'https://rubygems.org'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby '3.1.2'

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem 'rails', '~> 7.0.4', '>= 7.0.4.3'

# Use postgresql as the database for Active Record
gem 'pg', '~> 1.1'

# Use the Puma web server [https://github.com/puma/puma]
gem 'puma', '~> 5.0'

# Build JSON APIs with ease [https://github.com/rails/jbuilder]
# gem "jbuilder"

# Use Redis adapter to run Action Cable in production
# gem "redis", "~> 4.0"

# Use Kredis to get higher-level data types in Redis [https://github.com/rails/kredis]
# gem "kredis"

# Use Active Model has_secure_password [https://guides.rubyonrails.org/active_model_basics.html#securepassword]
gem 'bcrypt', '~> 3.1.7'

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem 'tzinfo-data', platforms: %i[mingw mswin x64_mingw jruby]

# Reduces boot times through caching; required in config/boot.rb
gem 'bootsnap', require: false

# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
# gem "image_processing", "~> 1.2"

# Use Rack CORS for handling Cross-Origin Resource Sharing (CORS), making cross-origin AJAX possible
gem 'aws-sdk-s3'
gem 'discard', '~> 1.2'
gem 'dotenv'
gem 'faraday'
gem 'honeybadger', '~> 4.0'
gem 'jwt'
gem 'kaminari'
gem 'metainspector'
gem 'open-uri'
gem 'rack-cors'
gem 'redis', '~> 5.0'
gem 'request_store', '~> 1.5'
gem 'request_store-sidekiq', '0.1.0'
gem 'rest-client'
gem 'ruby-openai', '7.1.0'
gem 'sentry-rails', '~> 4.8', '>= 4.8.1'
gem 'sentry-ruby', '~> 4.8', '>= 4.8.1'
gem 'sentry-sidekiq', '~> 4.8', '>= 4.8.1'
gem 'sidekiq'
gem 'sidekiq-cron'
gem 'tiktoken_ruby'

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem 'debug', platforms: %i[mri mingw x64_mingw]
  gem 'letter_opener'
  gem 'pry-rails'
  gem 'rspec-json_expectations', '~> 2.2'
  gem 'rspec-rails', '~> 7.0.0'
  gem 'shoulda-matchers'

  gem 'rubocop', require: false
  gem 'rubocop-performance', require: false
  gem 'rubocop-rails', require: false
  gem 'rubocop-rspec', require: false
end

group :development do
  # Speed up commands on slow machines / big apps [https://github.com/rails/spring]
  # gem "spring"
  gem 'dockerfile-rails', '>= 1.5'
end

group :test do
  gem 'database_cleaner'
end
